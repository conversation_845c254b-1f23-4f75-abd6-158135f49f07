@page "{id:int}"
@using LibraryManagementSystem.DAL.Models.Enums
@model LibraryManagementSystem.UI.Pages.Books.DetailsModel
@{
    ViewData["Title"] = Model.Book?.Title ?? "تفاصيل الكتاب - Book Details";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <!-- رسائل التنبيه - Alert Messages -->
            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @Model.ErrorMessage
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.SuccessMessage))
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @Model.SuccessMessage
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (Model.Book != null)
            {
                <div class="card shadow-sm">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-book me-2"></i>
                            تفاصيل الكتاب - Book Details
                        </h3>
                        <div>
                            <a href="/Books" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-right me-1"></i>
                                العودة للقائمة - Back to List
                            </a>
                            @if (Model.IsAdmin)
                            {
                                <a href="/Books/Edit/@Model.Book.BookId" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit me-1"></i>
                                    تعديل - Edit
                                </a>
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- معلومات أساسية - Basic Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h2 class="text-primary mb-3">@Model.Book.Title</h2>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <table class="table table-borderless">
                                                    <tr>
                                                        <td class="fw-bold text-muted" style="width: 40%;">
                                                            <i class="fas fa-user me-1"></i>
                                                            المؤلف - Author:
                                                        </td>
                                                        <td>@Model.Book.Author</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-bold text-muted">
                                                            <i class="fas fa-barcode me-1"></i>
                                                            الرقم المعياري - ISBN:
                                                        </td>
                                                        <td><code>@Model.Book.ISBN</code></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-bold text-muted">
                                                            <i class="fas fa-building me-1"></i>
                                                            الناشر - Publisher:
                                                        </td>
                                                        <td>@(Model.Book.Publisher ?? "غير محدد - Not specified")</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-bold text-muted">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            سنة النشر - Publication Year:
                                                        </td>
                                                        <td>@Model.Book.PublicationYear</td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <div class="col-md-6">
                                                <table class="table table-borderless">
                                                    <tr>
                                                        <td class="fw-bold text-muted" style="width: 40%;">
                                                            <i class="fas fa-tags me-1"></i>
                                                            النوع - Genre:
                                                        </td>
                                                        <td>
                                                            @if (!string.IsNullOrEmpty(Model.Book.Genre))
                                                            {
                                                                <span class="badge bg-secondary">@Model.Book.Genre</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="text-muted">غير محدد - Not specified</span>
                                                            }
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-bold text-muted">
                                                            <i class="fas fa-copy me-1"></i>
                                                            إجمالي النسخ - Total Copies:
                                                        </td>
                                                        <td><span class="badge bg-info">@Model.Book.TotalCopies</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-bold text-muted">
                                                            <i class="fas fa-check-circle me-1"></i>
                                                            النسخ المتاحة - Available Copies:
                                                        </td>
                                                        <td><span class="badge bg-success">@Model.Book.AvailableCopies</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-bold text-muted">
                                                            <i class="fas fa-user-clock me-1"></i>
                                                            النسخ المُعارة - Borrowed Copies:
                                                        </td>
                                                        <td><span class="badge bg-warning">@Model.Book.BorrowedCopies</span></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الوصف - Description -->
                                @if (!string.IsNullOrEmpty(Model.Book.Description))
                                {
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h5 class="text-secondary mb-3">
                                                <i class="fas fa-align-left me-2"></i>
                                                الوصف - Description
                                            </h5>
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <p class="mb-0">@Model.Book.Description</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>

                            <div class="col-md-4">
                                <!-- حالة التوفر - Availability Status -->
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">
                                            <i class="fas fa-info-circle me-1"></i>
                                            حالة التوفر - Availability Status
                                        </h6>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <span class="@Model.Book.AvailabilityStatus.GetCssClass() fs-5">
                                                <i class="@Model.Book.AvailabilityStatus.GetIcon() me-2"></i>
                                                @Model.Book.AvailabilityStatusText
                                            </span>
                                        </div>
                                        
                                        @if (Model.Book.IsAvailable)
                                        {
                                            <form method="post" asp-page-handler="Borrow">
                                                <input type="hidden" name="bookId" value="@Model.Book.BookId" />
                                                <button type="submit" class="btn btn-primary btn-lg w-100"
                                                        onclick="return confirm('هل تريد استعارة الكتاب: @Model.Book.Title؟\n\nفترة الاستعارة: 14 يوم\n\nDo you want to borrow: @Model.Book.Title?\nBorrowing period: 14 days')">
                                                    <i class="fas fa-book-reader me-2"></i>
                                                    استعارة الكتاب - Borrow Book
                                                </button>
                                            </form>
                                        }
                                        else
                                        {
                                            <button type="button" class="btn btn-secondary btn-lg w-100" disabled>
                                                <i class="fas fa-ban me-2"></i>
                                                غير متوفر للاستعارة - Not Available
                                            </button>
                                        }
                                    </div>
                                </div>

                                <!-- معلومات إضافية - Additional Information -->
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">
                                            <i class="fas fa-clock me-1"></i>
                                            معلومات إضافية - Additional Info
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <small class="text-muted">
                                            <div class="mb-2">
                                                <i class="fas fa-plus-circle me-1"></i>
                                                تاريخ الإضافة - Added: @Model.Book.CreatedDate.ToString("dd/MM/yyyy")
                                            </div>
                                            @if (Model.Book.ModifiedDate != Model.Book.CreatedDate)
                                            {
                                                <div class="mb-2">
                                                    <i class="fas fa-edit me-1"></i>
                                                    آخر تعديل - Modified: @Model.Book.ModifiedDate.ToString("dd/MM/yyyy")
                                                </div>
                                            }
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لم يتم العثور على الكتاب المطلوب - The requested book was not found
                </div>
                <div class="text-center">
                    <a href="/Books" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة - Back to List
                    </a>
                </div>
            }
        </div>
    </div>
</div>
