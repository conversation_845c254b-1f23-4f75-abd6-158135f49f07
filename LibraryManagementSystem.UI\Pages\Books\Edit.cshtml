@page "{id:int}"
@model LibraryManagementSystem.UI.Pages.Books.EditModel
@{
    ViewData["Title"] = "تعديل الكتاب - Edit Book";
}

<div class="container-fluid" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الكتاب - Edit Book
                    </h3>
                    <div>
                        <a href="/Books/Details/@Model.Input.BookId" class="btn btn-info btn-sm">
                            <i class="fas fa-eye me-1"></i>
                            عرض التفاصيل - View Details
                        </a>
                        <a href="/Books" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للقائمة - Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- رسائل التنبيه - Alert Messages -->
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @Model.ErrorMessage
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            يرجى تصحيح الأخطاء التالية - Please correct the following errors:
                            <ul class="mb-0 mt-2">
                                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                                {
                                    <li>@error.ErrorMessage</li>
                                }
                            </ul>
                        </div>
                    }

                    @if (Model.Input.BookId > 0)
                    {
                        <form method="post">
                            <input type="hidden" asp-for="Input.BookId" />
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- معلومات أساسية - Basic Information -->
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        المعلومات الأساسية - Basic Information
                                    </h5>

                                    <div class="mb-3">
                                        <label asp-for="Input.Title" class="form-label">
                                            <i class="fas fa-book me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.Title) <span class="text-danger">*</span>
                                        </label>
                                        <input asp-for="Input.Title" class="form-control" />
                                        <span asp-validation-for="Input.Title" class="text-danger"></span>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="Input.Author" class="form-label">
                                            <i class="fas fa-user me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.Author) <span class="text-danger">*</span>
                                        </label>
                                        <input asp-for="Input.Author" class="form-control" />
                                        <span asp-validation-for="Input.Author" class="text-danger"></span>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="Input.ISBN" class="form-label">
                                            <i class="fas fa-barcode me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.ISBN) <span class="text-danger">*</span>
                                        </label>
                                        <input asp-for="Input.ISBN" class="form-control" />
                                        <span asp-validation-for="Input.ISBN" class="text-danger"></span>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            الرقم المعياري الدولي للكتاب (10 أو 13 رقم)
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="Input.Publisher" class="form-label">
                                            <i class="fas fa-building me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.Publisher)
                                        </label>
                                        <input asp-for="Input.Publisher" class="form-control" />
                                        <span asp-validation-for="Input.Publisher" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- تفاصيل إضافية - Additional Details -->
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-cogs me-2"></i>
                                        التفاصيل الإضافية - Additional Details
                                    </h5>

                                    <div class="mb-3">
                                        <label asp-for="Input.PublicationYear" class="form-label">
                                            <i class="fas fa-calendar me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.PublicationYear) <span class="text-danger">*</span>
                                        </label>
                                        <input asp-for="Input.PublicationYear" type="number" class="form-control" min="1000" max="2100" />
                                        <span asp-validation-for="Input.PublicationYear" class="text-danger"></span>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="Input.Genre" class="form-label">
                                            <i class="fas fa-tags me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.Genre)
                                        </label>
                                        <select asp-for="Input.Genre" class="form-select">
                                            <option value="">اختر نوع الكتاب - Select Genre</option>
                                            <option value="أدب">أدب - Literature</option>
                                            <option value="رواية">رواية - Novel</option>
                                            <option value="خيال علمي">خيال علمي - Science Fiction</option>
                                            <option value="تاريخ">تاريخ - History</option>
                                            <option value="فكر">فكر - Philosophy</option>
                                            <option value="سيرة ذاتية">سيرة ذاتية - Biography</option>
                                            <option value="قصص قصيرة">قصص قصيرة - Short Stories</option>
                                            <option value="إثارة">إثارة - Thriller</option>
                                            <option value="علوم">علوم - Science</option>
                                            <option value="تكنولوجيا">تكنولوجيا - Technology</option>
                                            <option value="دين">دين - Religion</option>
                                            <option value="طبخ">طبخ - Cooking</option>
                                            <option value="رياضة">رياضة - Sports</option>
                                            <option value="فن">فن - Art</option>
                                            <option value="موسيقى">موسيقى - Music</option>
                                        </select>
                                        <span asp-validation-for="Input.Genre" class="text-danger"></span>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label asp-for="Input.TotalCopies" class="form-label">
                                                    <i class="fas fa-copy me-1"></i>
                                                    @Html.DisplayNameFor(m => m.Input.TotalCopies) <span class="text-danger">*</span>
                                                </label>
                                                <input asp-for="Input.TotalCopies" type="number" class="form-control" min="1" max="1000" />
                                                <span asp-validation-for="Input.TotalCopies" class="text-danger"></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label asp-for="Input.AvailableCopies" class="form-label">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    @Html.DisplayNameFor(m => m.Input.AvailableCopies) <span class="text-danger">*</span>
                                                </label>
                                                <input asp-for="Input.AvailableCopies" type="number" class="form-control" min="0" max="1000" />
                                                <span asp-validation-for="Input.AvailableCopies" class="text-danger"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-1"></i>
                                        <small>
                                            النسخ المُعارة: <strong>@(Model.Input.TotalCopies - Model.Input.AvailableCopies)</strong>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- الوصف - Description -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label asp-for="Input.Description" class="form-label">
                                            <i class="fas fa-align-left me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.Description)
                                        </label>
                                        <textarea asp-for="Input.Description" class="form-control" rows="4"></textarea>
                                        <span asp-validation-for="Input.Description" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات - Action Buttons -->
                            <div class="row">
                                <div class="col-12">
                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <button type="submit" class="btn btn-warning btn-lg">
                                                <i class="fas fa-save me-2"></i>
                                                حفظ التغييرات - Save Changes
                                            </button>
                                        </div>
                                        <div>
                                            <a href="/Books/Details/@Model.Input.BookId" class="btn btn-outline-info btn-lg me-2">
                                                <i class="fas fa-eye me-2"></i>
                                                عرض التفاصيل - View Details
                                            </a>
                                            <a href="/Books" class="btn btn-outline-primary btn-lg">
                                                <i class="fas fa-times me-2"></i>
                                                إلغاء - Cancel
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    }
                    else
                    {
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لم يتم العثور على الكتاب المطلوب - The requested book was not found
                        </div>
                        <div class="text-center">
                            <a href="/Books" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-1"></i>
                                العودة للقائمة - Back to List
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
