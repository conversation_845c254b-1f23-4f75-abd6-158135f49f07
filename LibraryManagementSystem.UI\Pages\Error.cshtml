@page
@model ErrorModel
@{
    ViewData["Title"] = Model.ErrorResponse?.Title ?? "خطأ - Error";
    Layout = "_Layout";
}

<div class="container-fluid" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="text-center mt-5">
                <!-- أيقونة الخطأ - Error Icon -->
                <div class="mb-4">
                    @if (Model.StatusCode == 404)
                    {
                        <i class="fas fa-search text-warning" style="font-size: 6rem;"></i>
                    }
                    else if (Model.StatusCode == 401 || Model.StatusCode == 403)
                    {
                        <i class="fas fa-lock text-danger" style="font-size: 6rem;"></i>
                    }
                    else if (Model.StatusCode == 408 || Model.StatusCode == 504)
                    {
                        <i class="fas fa-clock text-info" style="font-size: 6rem;"></i>
                    }
                    else
                    {
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 6rem;"></i>
                    }
                </div>

                <!-- عنوان الخطأ - Error Title -->
                <h1 class="display-4 text-dark mb-3">
                    @if (Model.StatusCode.HasValue)
                    {
                        <span class="text-muted">@Model.StatusCode</span>
                    }
                </h1>

                <h2 class="h3 text-primary mb-4">
                    @(Model.ErrorResponse?.Title ?? "خطأ غير متوقع - Unexpected Error")
                </h2>

                <!-- رسالة الخطأ - Error Message -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <p class="card-text lead text-muted">
                            @(Model.ErrorResponse?.Message ?? "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.")
                        </p>

                        @if (!string.IsNullOrEmpty(Model.ErrorResponse?.ErrorId))
                        {
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    معرف الخطأ للمرجع - Error ID for reference: 
                                    <code>@Model.ErrorResponse.ErrorId</code>
                                </small>
                            </div>
                        }

                        @if (Model.ErrorResponse?.Timestamp != null)
                        {
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    وقت الحدوث - Time: @Model.ErrorResponse.Timestamp.ToString("yyyy-MM-dd HH:mm:ss UTC")
                                </small>
                            </div>
                        }
                    </div>
                </div>

                <!-- تفاصيل إضافية للتطوير - Additional details for development -->
                @if (!string.IsNullOrEmpty(Model.ErrorResponse?.Details) && 
                     ViewContext.HttpContext.RequestServices.GetService<IWebHostEnvironment>()?.IsDevelopment() == true)
                {
                    <div class="card border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-code me-1"></i>
                                تفاصيل التطوير - Development Details
                            </h6>
                        </div>
                        <div class="card-body">
                            <pre class="text-start small text-muted">@Model.ErrorResponse.Details</pre>
                        </div>
                    </div>
                }

                <!-- أزرار الإجراءات - Action Buttons -->
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <button type="button" class="btn btn-primary btn-lg" onclick="history.back()">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للخلف - Go Back
                            </button>
                            
                            <a href="/" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-home me-2"></i>
                                الصفحة الرئيسية - Home
                            </a>

                            @if (Model.StatusCode == 404)
                            {
                                <a href="/Books" class="btn btn-outline-success btn-lg">
                                    <i class="fas fa-book me-2"></i>
                                    تصفح الكتب - Browse Books
                                </a>
                            }
                            else if (Model.StatusCode == 401)
                            {
                                <a href="/Auth/Login" class="btn btn-outline-info btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول - Login
                                </a>
                            }
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية - Additional Information -->
                <div class="mt-5">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title text-primary">
                                <i class="fas fa-question-circle me-1"></i>
                                هل تحتاج مساعدة؟ - Need Help?
                            </h6>
                            <p class="card-text small text-muted mb-2">
                                إذا استمر هذا الخطأ، يرجى الاتصال بالدعم الفني مع تقديم معرف الخطأ أعلاه.
                                <br>
                                If this error persists, please contact technical support with the error ID above.
                            </p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="mailto:<EMAIL>" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني - Email
                                </a>
                                <a href="tel:+966-11-1234567" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-phone me-1"></i>
                                    الهاتف - Phone
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معرف الطلب للتطوير - Request ID for development -->
                @if (Model.ShowRequestId)
                {
                    <div class="mt-3">
                        <small class="text-muted">
                            Request ID: <code>@Model.RequestId</code>
                        </small>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // إعادة تحميل الصفحة تلقائياً في حالة أخطاء الخادم (اختياري)
        // Auto-refresh page for server errors (optional)
        @if (Model.StatusCode >= 500)
        {
            <text>
            // إعادة تحميل تلقائية بعد 30 ثانية للأخطاء الخادم
            // Auto-refresh after 30 seconds for server errors
            setTimeout(function() {
                if (confirm('هل تريد إعادة تحميل الصفحة؟ - Would you like to refresh the page?')) {
                    location.reload();
                }
            }, 30000);
            </text>
        }

        // تسجيل الخطأ في وحدة التحكم للتطوير
        // Log error to console for development
        @if (!string.IsNullOrEmpty(Model.ErrorResponse?.ErrorId))
        {
            <text>
            console.error('Error ID: @Model.ErrorResponse.ErrorId', {
                statusCode: @(Model.StatusCode ?? 0),
                title: '@Html.Raw(Model.ErrorResponse.Title)',
                message: '@Html.Raw(Model.ErrorResponse.Message)',
                timestamp: '@Model.ErrorResponse.Timestamp'
            });
            </text>
        }
    </script>
}
