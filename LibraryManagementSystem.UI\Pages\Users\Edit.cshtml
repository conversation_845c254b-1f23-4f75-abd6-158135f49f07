@page "{id:int}"
@using LibraryManagementSystem.DAL.Models.Enums
@model LibraryManagementSystem.UI.Pages.Users.EditModel
@{
    ViewData["Title"] = "تعديل المستخدم - Edit User";
}

<div class="container-fluid" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        تعديل المستخدم - Edit User
                    </h3>
                    <a href="/Users" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة - Back to List
                    </a>
                </div>
                <div class="card-body">
                    <!-- رسائل التنبيه - Alert Messages -->
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @Model.ErrorMessage
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            يرجى تصحيح الأخطاء التالية - Please correct the following errors:
                            <ul class="mb-0 mt-2">
                                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                                {
                                    <li>@error.ErrorMessage</li>
                                }
                            </ul>
                        </div>
                    }

                    @if (Model.Input.UserId > 0)
                    {
                        <form method="post">
                            <input type="hidden" asp-for="Input.UserId" />
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- معلومات شخصية - Personal Information -->
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-user me-2"></i>
                                        المعلومات الشخصية - Personal Information
                                    </h5>

                                    <div class="mb-3">
                                        <label asp-for="Input.FirstName" class="form-label">
                                            <i class="fas fa-user me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.FirstName) <span class="text-danger">*</span>
                                        </label>
                                        <input asp-for="Input.FirstName" class="form-control" />
                                        <span asp-validation-for="Input.FirstName" class="text-danger"></span>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="Input.LastName" class="form-label">
                                            <i class="fas fa-user me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.LastName) <span class="text-danger">*</span>
                                        </label>
                                        <input asp-for="Input.LastName" class="form-control" />
                                        <span asp-validation-for="Input.LastName" class="text-danger"></span>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="Input.Email" class="form-label">
                                            <i class="fas fa-envelope me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.Email) <span class="text-danger">*</span>
                                        </label>
                                        <input asp-for="Input.Email" type="email" class="form-control" />
                                        <span asp-validation-for="Input.Email" class="text-danger"></span>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="Input.PhoneNumber" class="form-label">
                                            <i class="fas fa-phone me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.PhoneNumber)
                                        </label>
                                        <input asp-for="Input.PhoneNumber" type="tel" class="form-control" />
                                        <span asp-validation-for="Input.PhoneNumber" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- إعدادات الحساب - Account Settings -->
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-cogs me-2"></i>
                                        إعدادات الحساب - Account Settings
                                    </h5>

                                    <div class="mb-3">
                                        <label asp-for="Input.Role" class="form-label">
                                            <i class="fas fa-user-tag me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.Role) <span class="text-danger">*</span>
                                        </label>
                                        <select asp-for="Input.Role" class="form-select">
                                            <option value="@UserRole.User">@UserRole.User.GetDescription()</option>
                                            <option value="@UserRole.Administrator">@UserRole.Administrator.GetDescription()</option>
                                        </select>
                                        <span asp-validation-for="Input.Role" class="text-danger"></span>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            اختر دور المستخدم في النظام
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input asp-for="Input.IsActive" class="form-check-input" type="checkbox" />
                                            <label asp-for="Input.IsActive" class="form-check-label">
                                                <i class="fas fa-toggle-on me-1"></i>
                                                @Html.DisplayNameFor(m => m.Input.IsActive)
                                            </label>
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            المستخدمون غير النشطين لا يمكنهم تسجيل الدخول
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="Input.Address" class="form-label">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            @Html.DisplayNameFor(m => m.Input.Address)
                                        </label>
                                        <textarea asp-for="Input.Address" class="form-control" rows="3"></textarea>
                                        <span asp-validation-for="Input.Address" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات إضافية - Additional Information -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>ملاحظة:</strong> لتغيير كلمة المرور، يرجى استخدام صفحة إعادة تعيين كلمة المرور المنفصلة.
                                        <br>
                                        <strong>Note:</strong> To change the password, please use the separate password reset page.
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات - Action Buttons -->
                            <div class="row">
                                <div class="col-12">
                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <button type="submit" class="btn btn-warning btn-lg">
                                                <i class="fas fa-save me-2"></i>
                                                حفظ التغييرات - Save Changes
                                            </button>
                                        </div>
                                        <div>
                                            <a href="/Users" class="btn btn-outline-primary btn-lg">
                                                <i class="fas fa-times me-2"></i>
                                                إلغاء - Cancel
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    }
                    else
                    {
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لم يتم العثور على المستخدم المطلوب - The requested user was not found
                        </div>
                        <div class="text-center">
                            <a href="/Users" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-1"></i>
                                العودة للقائمة - Back to List
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
